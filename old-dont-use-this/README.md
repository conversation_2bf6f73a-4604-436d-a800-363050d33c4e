# Dynamic SSH Connection Ansible Playbooks

This repository contains Ansible playbooks for automatically detecting and connecting to hosts with unknown operating systems and SSH configurations. The playbooks intelligently try different SSH users and authentication methods to establish connections.

## 🏗️ Structure

```
├── lab/                          # Lab environment
│   ├── dynamic-ssh-connection.yml       # Basic dynamic SSH playbook
│   ├── enhanced-dynamic-ssh-connection.yml  # Enhanced with OpenStack metadata
│   ├── inventory/
│   │   └── hosts.yml            # Lab inventory example
│   ├── roles/
│   │   └── setup-ssh-config-lab/
│   │       ├── tasks/main.yml   # SSH setup for lab
│   │       └── files/           # SSH keys for lab
│   └── tasks/                   # Reusable task files
│
├── production/                   # Production environment
│   ├── dynamic-ssh-connection.yml       # Basic dynamic SSH playbook
│   ├── inventory/
│   │   └── hosts.yml            # Production inventory example
│   ├── roles/
│   │   └── setup-ssh-config-production/
│   │       ├── tasks/main.yml   # SSH setup for production
│   │       └── files/           # SSH keys for production
│   └── tasks/                   # Reusable task files
│
└── README.md                    # This documentation
```

## 🚀 Features

### Core Functionality
- **Dynamic User Detection**: Automatically tries `ubuntu`, `cloud-user`, and `centos` users
- **Multiple Authentication Methods**: Supports SSH keys and passwordless authentication
- **Intelligent Fallback**: Gracefully falls back between different connection methods
- **OS Detection**: Maps detected OS to appropriate default users
- **Connection Caching**: Stores successful connection details for reuse

### Enhanced Features
- **OpenStack Metadata Integration**: Detects OS from OpenStack metadata when available
- **Comprehensive Logging**: Detailed connection attempt logging
- **Error Handling**: Robust error handling with informative failure messages
- **Connection Validation**: Verifies OS detection accuracy

## 🔧 Configuration

### Lab Environment
- **SSH Keys**: `controller.pem`, `labms.pem`
- **Users**: `ubuntu`, `cloud-user`, `centos`
- **Timeout**: 10 seconds

### Production Environment
- **SSH Keys**: `devops.pem`
- **Users**: `ubuntu`, `cloud-user`, `centos`
- **Timeout**: 15 seconds

## 📋 Usage

### Lab Environment

#### Basic Dynamic Connection
```bash
ansible-playbook -i lab/inventory/hosts.yml lab/dynamic-ssh-connection.yml
```

#### Enhanced Connection (with OpenStack metadata)
```bash
ansible-playbook -i lab/inventory/hosts.yml lab/enhanced-dynamic-ssh-connection.yml
```

### Production Environment

```bash
ansible-playbook -i production/inventory/hosts.yml production/dynamic-ssh-connection.yml
```

### Targeting Specific Hosts

```bash
# Target specific host
ansible-playbook -i lab/inventory/hosts.yml lab/dynamic-ssh-connection.yml --limit lab-ubuntu-01

# Target specific group
ansible-playbook -i lab/inventory/hosts.yml lab/dynamic-ssh-connection.yml --limit lab_web_servers
```

## 🔑 SSH Key Setup

### Lab Environment
1. Place your SSH private keys in `lab/roles/setup-ssh-config-lab/files/`:
   - `controller.pem`
   - `labms.pem`

2. Ensure proper permissions:
   ```bash
   chmod 400 lab/roles/setup-ssh-config-lab/files/*.pem
   ```

### Production Environment
1. Place your SSH private key in `production/roles/setup-ssh-config-production/files/`:
   - `devops.pem`

2. Ensure proper permissions:
   ```bash
   chmod 400 production/roles/setup-ssh-config-production/files/*.pem
   ```

## 📊 Output

The playbooks generate detailed connection information:

### Console Output
```
✓ Successfully connected to lab-ubuntu-01 as ubuntu using controller.pem
Successfully connected to lab-ubuntu-01:
- User: ubuntu
- OS: Ubuntu 20.04
- Architecture: x86_64
- SSH Key: controller.pem
- FQDN: lab-ubuntu-01.example.com
```

### Generated Files
Connection details are saved to `/tmp/` for each host:
- `{hostname}_connection_info.yml` - Basic connection details
- `{hostname}_enhanced_connection_info.yml` - Enhanced details with metadata

## 🔍 Troubleshooting

### Common Issues

1. **SSH Key Not Found**
   ```
   TASK [Copy SSH keys] ***
   fatal: [localhost]: FAILED! => {"msg": "Could not find or access 'controller.pem'"}
   ```
   **Solution**: Ensure SSH keys are placed in the correct `files/` directory

2. **All Connection Methods Failed**
   ```
   Failed to establish SSH connection using any of the following methods:
   - Users tried: ubuntu, cloud-user, centos
   ```
   **Solution**: 
   - Verify host is reachable: `ping <hostname>`
   - Check SSH service: `telnet <hostname> 22`
   - Verify user accounts exist on target system

3. **Permission Denied**
   ```
   Permission denied (publickey)
   ```
   **Solution**:
   - Verify SSH key permissions: `ls -la *.pem`
   - Ensure public key is in `~/.ssh/authorized_keys` on target host
   - Check SSH key format and validity

### Debug Mode

Run with increased verbosity for debugging:
```bash
ansible-playbook -i lab/inventory/hosts.yml lab/dynamic-ssh-connection.yml -vvv
```

## 🎯 Best Practices

1. **Key Management**
   - Use separate keys for different environments
   - Regularly rotate SSH keys
   - Store keys securely (consider Ansible Vault)

2. **Inventory Organization**
   - Group hosts by function and environment
   - Use descriptive host names
   - Document expected OS/user when known

3. **Error Handling**
   - Monitor playbook execution logs
   - Set appropriate timeouts for your network
   - Use `--check` mode for dry runs

## 🔒 Security Considerations

- SSH keys are copied to `/runner/.ssh/` with restrictive permissions (0400)
- `StrictHostKeyChecking` is disabled for automation (consider security implications)
- Connection details are logged (review for sensitive information)
- Use Ansible Vault for sensitive variables in production

## 🤝 Contributing

1. Test changes in lab environment first
2. Update documentation for new features
3. Follow existing code structure and naming conventions
4. Add error handling for new functionality
