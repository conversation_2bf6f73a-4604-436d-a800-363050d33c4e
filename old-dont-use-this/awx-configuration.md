# AWX Configuration Guide

This guide explains how to configure these dynamic SSH connection playbooks in AWX (Ansible Tower).

## 📋 Prerequisites

1. AWX/Ansible Tower installed and configured
2. SSH private keys available
3. Target hosts accessible from AWX

## 🔧 AWX Setup

### 1. Create Credentials

#### SSH Private Keys
Create separate credentials for each environment:

**Lab Environment Credentials:**
- **Name**: `Lab SSH Keys`
- **Type**: `Machine`
- **Username**: `runner` (or leave empty)
- **SSH Private Key**: Paste content of `controller.pem`

**Additional Lab Key:**
- **Name**: `Lab SSH Keys - LABMS`
- **Type**: `Machine`
- **Username**: `runner`
- **SSH Private Key**: Paste content of `labms.pem`

**Production Environment Credentials:**
- **Name**: `Production SSH Keys`
- **Type**: `Machine`
- **Username**: `runner`
- **SSH Private Key**: Paste content of `devops.pem`

### 2. Create Projects

#### Lab Project
- **Name**: `Dynamic SSH - Lab`
- **SCM Type**: `Git`
- **SCM URL**: `<your-repository-url>`
- **SCM Branch**: `main`
- **Playbook Directory**: `lab/`

#### Production Project
- **Name**: `Dynamic SSH - Production`
- **SCM Type**: `Git`
- **SCM URL**: `<your-repository-url>`
- **SCM Branch**: `main`
- **Playbook Directory**: `production/`

### 3. Create Inventories

#### Lab Inventory
- **Name**: `Lab Hosts`
- **Import from**: Upload `lab/inventory/hosts.yml`

#### Production Inventory
- **Name**: `Production Hosts`
- **Import from**: Upload `production/inventory/hosts.yml`

### 4. Create Job Templates

#### Lab Dynamic SSH Connection
- **Name**: `Lab - Dynamic SSH Connection`
- **Job Type**: `Run`
- **Inventory**: `Lab Hosts`
- **Project**: `Dynamic SSH - Lab`
- **Playbook**: `dynamic-ssh-connection.yml`
- **Credentials**: 
  - `Lab SSH Keys`
  - `Lab SSH Keys - LABMS`
- **Variables**:
  ```yaml
  ---
  ansible_ssh_common_args: "-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null"
  ssh_timeout: 10
  ```

#### Lab Enhanced Dynamic SSH Connection
- **Name**: `Lab - Enhanced Dynamic SSH Connection`
- **Job Type**: `Run`
- **Inventory**: `Lab Hosts`
- **Project**: `Dynamic SSH - Lab`
- **Playbook**: `enhanced-dynamic-ssh-connection.yml`
- **Credentials**: 
  - `Lab SSH Keys`
  - `Lab SSH Keys - LABMS`

#### Production Dynamic SSH Connection
- **Name**: `Production - Dynamic SSH Connection`
- **Job Type**: `Run`
- **Inventory**: `Production Hosts`
- **Project**: `Dynamic SSH - Production`
- **Playbook**: `dynamic-ssh-connection.yml`
- **Credentials**: `Production SSH Keys`
- **Variables**:
  ```yaml
  ---
  ansible_ssh_common_args: "-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null"
  ssh_timeout: 15
  ```

#### Connection Test Template
- **Name**: `Lab - Test Connections`
- **Job Type**: `Run`
- **Inventory**: `Lab Hosts`
- **Project**: `Dynamic SSH - Lab`
- **Playbook**: `test-connections.yml`
- **Credentials**: 
  - `Lab SSH Keys`
  - `Lab SSH Keys - LABMS`

### 5. Create Workflows (Optional)

#### Lab Discovery Workflow
1. **Test Connections** → **Dynamic SSH Connection** → **Your Application Playbook**

#### Production Deployment Workflow
1. **Dynamic SSH Connection** → **System Updates** → **Application Deployment**

## 🎯 Usage in AWX

### Running Jobs

1. **Navigate to Templates**
2. **Select desired template**
3. **Click Launch**
4. **Monitor execution in real-time**

### Limiting Execution

Use the **Limit** field in job launch to target specific hosts:
- Single host: `lab-ubuntu-01`
- Multiple hosts: `lab-ubuntu-01,lab-centos-01`
- Group: `lab_web_servers`

### Viewing Results

1. **Job Output**: Real-time execution logs
2. **Artifacts**: Generated connection info files in `/tmp/`
3. **Facts**: Gathered system information

## 🔍 Monitoring and Troubleshooting

### Job Status Indicators
- ✅ **Successful**: All hosts connected successfully
- ⚠️ **Changed**: Some hosts required different connection methods
- ❌ **Failed**: Unable to connect to one or more hosts

### Common AWX Issues

1. **Credential Access**
   ```
   ERROR! The field 'ssh_private_key_file' has an invalid value
   ```
   **Solution**: Verify credentials are properly configured and assigned to job template

2. **Project Sync Issues**
   ```
   ERROR! the playbook: dynamic-ssh-connection.yml could not be found
   ```
   **Solution**: Ensure project is synced and playbook directory is correct

3. **Inventory Import Errors**
   ```
   ERROR! Inventory import failed
   ```
   **Solution**: Validate YAML syntax in inventory files

### Debug Mode in AWX

1. **Enable Verbose Output**: Set verbosity to 3+ in job template
2. **Check Execution Environment**: Verify EE has required packages
3. **Review Job Logs**: Check stdout and stderr for detailed error messages

## 🔒 Security Best Practices

### Credential Management
- Use separate credentials for each environment
- Regularly rotate SSH keys
- Limit credential access to necessary teams
- Consider using Ansible Vault for sensitive variables

### Network Security
- Ensure AWX can reach target hosts
- Configure firewall rules appropriately
- Use VPN or private networks when possible
- Monitor SSH access logs

### Audit and Compliance
- Enable job logging
- Review execution history regularly
- Document access patterns
- Implement approval workflows for production

## 📊 Performance Optimization

### Execution Environment
- Use custom EE with required packages pre-installed
- Optimize container image size
- Configure resource limits appropriately

### Parallel Execution
- Adjust `forks` setting for concurrent host processing
- Use `serial` for controlled rollouts
- Configure appropriate timeouts

### Caching
- Enable fact caching for repeated runs
- Use connection persistence where possible
- Cache successful connection methods

## 🚀 Advanced Features

### Survey Variables
Add survey to job templates for dynamic configuration:
- Target environment selection
- SSH timeout values
- User preference order
- Debug level selection

### Notifications
Configure notifications for:
- Job completion
- Connection failures
- Security events
- Performance thresholds

### Integration
- **ServiceNow**: Ticket creation for failed connections
- **Slack/Teams**: Real-time notifications
- **Grafana**: Performance monitoring
- **ELK Stack**: Log aggregation and analysis
