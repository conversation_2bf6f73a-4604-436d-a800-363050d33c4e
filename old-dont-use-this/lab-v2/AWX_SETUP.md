# AWX Setup Guide for SSH Multiple User Management

## Overview
This guide explains how to set up and configure AWX/Tower for the enhanced SSH multiple user management system.

## Prerequisites
- AWX/Tower instance running
- Access to AWX web interface
- SSH private keys for target hosts
- Understanding of AWX concepts (Projects, Inventories, Job Templates)

## Step 1: Create Project

1. Navigate to **Projects** in AWX
2. Click **Add** to create a new project
3. Configure the project:
   - **Name**: `SSH Multiple User Management`
   - **Organization**: Select your organization
   - **SCM Type**: `Git` (if using Git repository)
   - **SCM URL**: Your repository URL
   - **SCM Branch/Tag/Commit**: `main` or your preferred branch
   - **Update Revision on Launch**: ✅ (recommended)

## Step 2: Create Credentials

### SSH Private Keys
Create credentials for each SSH private key:

1. Navigate to **Credentials**
2. Click **Add** to create new credential
3. Configure for each key:
   - **Name**: `Controller SSH Key` / `LabMS SSH Key`
   - **Credential Type**: `Machine`
   - **SSH Private Key**: Paste your private key content
   - **Privilege Escalation Method**: `sudo`

### AWX Execution Environment Credential (if needed)
If using custom execution environment:
- **Name**: `Custom EE Credential`
- **Credential Type**: `Container Registry`

## Step 3: Create Inventories

### Development Inventory
1. Navigate to **Inventories**
2. Click **Add** → **Add Inventory**
3. Configure:
   - **Name**: `Development Hosts`
   - **Organization**: Select your organization
4. Add hosts or import from `inventory/development.yml`

### Production Inventory
1. Create another inventory:
   - **Name**: `Production Hosts`
2. Import from `inventory/production.yml`

## Step 4: Create Job Templates

### 1. Environment Setup Template
- **Name**: `Setup SSH Environment`
- **Job Type**: `Run`
- **Inventory**: `Development Hosts`
- **Project**: `SSH Multiple User Management`
- **Playbook**: `playbooks/setup-environment.yml`
- **Credentials**: Add your SSH credentials
- **Variables**:
  ```yaml
  awx_runner_path: /runner
  setup_validation: true
  ```

### 2. Gather Facts Template
- **Name**: `Gather Facts - Multiple Users`
- **Job Type**: `Run`
- **Inventory**: `Development Hosts` or `Production Hosts`
- **Project**: `SSH Multiple User Management`
- **Playbook**: `playbooks/gather-facts.yml`
- **Credentials**: Add your SSH credentials
- **Variables**:
  ```yaml
  log_level: INFO
  enable_debug: false
  save_facts_to_file: true
  ```

### 3. Connection Test Template
- **Name**: `Test Connections - Multiple Users`
- **Job Type**: `Run`
- **Inventory**: `Development Hosts` or `Production Hosts`
- **Project**: `SSH Multiple User Management`
- **Playbook**: `playbooks/test-connections.yml`
- **Credentials**: Add your SSH credentials
- **Variables**:
  ```yaml
  detailed_output: true
  generate_report: true
  test_sudo_access: true
  ```

## Step 5: Configure Execution Environment (Optional)

If you need custom packages or configurations:

1. Create custom execution environment with required packages
2. Update job templates to use the custom EE
3. Ensure SSH keys are properly mounted in the EE

## Step 6: Set Up Workflows (Advanced)

Create a workflow that:
1. Sets up SSH environment
2. Gathers facts from all hosts
3. Runs connection tests
4. Generates comprehensive reports

### Workflow Configuration:
1. Navigate to **Templates** → **Add** → **Add Workflow Job Template**
2. **Name**: `Complete SSH Setup and Testing`
3. Add nodes:
   - **Node 1**: Setup SSH Environment
   - **Node 2**: Gather Facts (on success of Node 1)
   - **Node 3**: Test Connections (on success of Node 2)

## Step 7: Schedule Regular Tasks (Optional)

Set up schedules for:
- Daily fact gathering
- Weekly connection tests
- Monthly environment validation

## Variables Reference

### Global Variables (set at inventory level):
```yaml
# SSH Configuration
ssh_users:
  - ubuntu
  - centos
  - cloud-user
  - ec2-user

# AWX specific
awx_runner_path: /runner
awx_ssh_path: /runner/.ssh

# Timeouts
connection_timeout: 30
gather_facts_timeout: 60

# Logging
log_level: INFO
enable_debug: false
```

### Job Template Variables:
```yaml
# For gather-facts.yml
save_facts_to_file: true
ansible_serial: "50%"  # Process 50% of hosts at a time

# For test-connections.yml
detailed_output: true
generate_report: true
test_sudo_access: true
connection_test_timeout: 30

# For setup-environment.yml
setup_validation: true
quick_test: true
```

## Troubleshooting

### Common Issues:

1. **SSH Key Permission Errors**
   - Ensure keys have correct permissions (400)
   - Verify keys are copied to the correct location

2. **Connection Timeouts**
   - Increase timeout values in variables
   - Check network connectivity
   - Verify SSH service is running on targets

3. **Fact Gathering Failures**
   - Check if target users exist
   - Verify SSH key access for each user
   - Review AWX job logs for specific errors

4. **Privilege Escalation Issues**
   - Ensure sudo access is configured
   - Check sudoers configuration on target hosts
   - Verify become method in credentials

### Debug Mode:
Enable debug mode by setting:
```yaml
enable_debug: true
log_level: DEBUG
```

## Security Considerations

1. **SSH Key Management**:
   - Use separate keys for different environments
   - Rotate keys regularly
   - Limit key access to necessary hosts only

2. **Credential Security**:
   - Use AWX credential encryption
   - Limit credential access to authorized users
   - Audit credential usage regularly

3. **Network Security**:
   - Use VPN or private networks when possible
   - Implement proper firewall rules
   - Monitor SSH access logs

## Monitoring and Alerting

Set up monitoring for:
- Failed job executions
- Connection test failures
- Fact gathering anomalies
- SSH key expiration

## Best Practices

1. **Testing**:
   - Always test in development environment first
   - Use limited host groups for initial testing
   - Validate changes before production deployment

2. **Documentation**:
   - Document any custom configurations
   - Maintain inventory documentation
   - Keep credential documentation updated

3. **Maintenance**:
   - Regular key rotation
   - Periodic connection testing
   - Update playbooks as needed
   - Monitor AWX logs for issues
