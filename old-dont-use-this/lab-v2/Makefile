# Makefile for SSH Multiple User Management Lab V2

.PHONY: help setup test gather-facts clean validate lint check-syntax

# Default target
help:
	@echo "SSH Multiple User Management - Lab V2"
	@echo "====================================="
	@echo ""
	@echo "Available targets:"
	@echo "  setup          - Setup SSH environment and validate configuration"
	@echo "  test           - Run connection tests on all hosts"
	@echo "  gather-facts   - Gather facts from all hosts"
	@echo "  validate       - Validate playbook syntax and configuration"
	@echo "  lint           - Run ansible-lint on all playbooks"
	@echo "  check-syntax   - Check playbook syntax"
	@echo "  clean          - Clean up temporary files and reports"
	@echo "  dev-setup      - Setup for development environment"
	@echo "  prod-setup     - Setup for production environment"
	@echo "  quick-test     - Quick connection test (ping only)"
	@echo ""
	@echo "Examples:"
	@echo "  make setup                    # Setup environment"
	@echo "  make test INVENTORY=dev       # Test with development inventory"
	@echo "  make gather-facts LIMIT=web   # Gather facts from web servers only"

# Variables
INVENTORY ?= development
INVENTORY_FILE = inventory/$(INVENTORY).yml
LIMIT ?= all
EXTRA_VARS ?= ""

# Setup targets
setup:
	@echo "Setting up SSH environment..."
	ansible-playbook playbooks/setup-environment.yml -i $(INVENTORY_FILE)

dev-setup:
	@echo "Setting up development environment..."
	ansible-playbook playbooks/setup-environment.yml -i inventory/development.yml

prod-setup:
	@echo "Setting up production environment..."
	ansible-playbook playbooks/setup-environment.yml -i inventory/production.yml

# Testing targets
test:
	@echo "Running connection tests..."
	ansible-playbook playbooks/test-connections.yml -i $(INVENTORY_FILE) --limit $(LIMIT) --extra-vars "$(EXTRA_VARS)"

quick-test:
	@echo "Running quick connection test..."
	ansible all -i $(INVENTORY_FILE) -m ping --limit $(LIMIT)

# Fact gathering
gather-facts:
	@echo "Gathering facts from hosts..."
	ansible-playbook playbooks/gather-facts.yml -i $(INVENTORY_FILE) --limit $(LIMIT) --extra-vars "$(EXTRA_VARS)"

# Validation targets
validate: check-syntax lint
	@echo "Validation complete!"

check-syntax:
	@echo "Checking playbook syntax..."
	@for playbook in playbooks/*.yml; do \
		echo "Checking $$playbook..."; \
		ansible-playbook --syntax-check $$playbook; \
	done

lint:
	@echo "Running ansible-lint..."
	@if command -v ansible-lint >/dev/null 2>&1; then \
		ansible-lint playbooks/ roles/; \
	else \
		echo "ansible-lint not found. Install with: pip install ansible-lint"; \
	fi

# Utility targets
clean:
	@echo "Cleaning up temporary files..."
	rm -f /tmp/*_facts.yml
	rm -f /tmp/*_connection_test.html
	rm -f /tmp/connection_test_summary.yml
	rm -f /tmp/environment_setup_report.md

inventory-list:
	@echo "Listing inventory hosts..."
	ansible-inventory -i $(INVENTORY_FILE) --list

inventory-graph:
	@echo "Showing inventory graph..."
	ansible-inventory -i $(INVENTORY_FILE) --graph

# SSH key management
copy-keys:
	@echo "Copying SSH keys (you need to specify source)..."
	@echo "Usage: make copy-keys CONTROLLER_KEY=/path/to/controller.pem LABMS_KEY=/path/to/labms.pem"
	@if [ -n "$(CONTROLLER_KEY)" ]; then \
		cp $(CONTROLLER_KEY) roles/ssh-setup/files/controller.pem; \
		chmod 400 roles/ssh-setup/files/controller.pem; \
		echo "Controller key copied"; \
	fi
	@if [ -n "$(LABMS_KEY)" ]; then \
		cp $(LABMS_KEY) roles/ssh-setup/files/labms.pem; \
		chmod 400 roles/ssh-setup/files/labms.pem; \
		echo "LabMS key copied"; \
	fi

check-keys:
	@echo "Checking SSH key files..."
	@ls -la roles/ssh-setup/files/*.pem 2>/dev/null || echo "No SSH keys found"

# Documentation
docs:
	@echo "Available documentation:"
	@echo "  README.md      - Main documentation"
	@echo "  QUICK_START.md - Quick start guide"
	@echo "  AWX_SETUP.md   - AWX configuration guide"

# Development helpers
dev-test:
	@echo "Running development tests..."
	make test INVENTORY=development EXTRA_VARS="detailed_output=true enable_debug=true"

prod-test:
	@echo "Running production tests..."
	make test INVENTORY=production

# Comprehensive workflow
full-setup: setup test gather-facts
	@echo "Full setup complete!"
	@echo "Check /tmp/ for generated reports"

# Debug mode
debug-test:
	@echo "Running tests in debug mode..."
	make test EXTRA_VARS="enable_debug=true log_level=DEBUG detailed_output=true"

# Show configuration
show-config:
	@echo "Current configuration:"
	@echo "  Inventory: $(INVENTORY_FILE)"
	@echo "  Limit: $(LIMIT)"
	@echo "  Extra vars: $(EXTRA_VARS)"
	@echo ""
	@echo "Available inventories:"
	@ls inventory/*.yml 2>/dev/null || echo "  No inventory files found"
