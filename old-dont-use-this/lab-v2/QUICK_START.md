# Quick Start Guide - Lab V2

## Overview
This is the enhanced version of SSH multiple user management with improved structure, better error handling, and AWX integration.

## Quick Setup (5 minutes)

### 1. Copy SSH Keys
```bash
# Copy your actual SSH private keys
cp /path/to/your/controller.pem lab-v2/roles/ssh-setup/files/controller.pem
cp /path/to/your/labms.pem lab-v2/roles/ssh-setup/files/labms.pem

# Set correct permissions
chmod 400 lab-v2/roles/ssh-setup/files/*.pem
```

### 2. Update Inventory
Edit `inventory/development.yml` with your actual hosts:
```yaml
ubuntu_hosts:
  hosts:
    your-ubuntu-host:
      ansible_host: ************
      preferred_user: ubuntu
```

### 3. Test Setup
```bash
cd lab-v2

# Setup environment
ansible-playbook playbooks/setup-environment.yml

# Test connections
ansible-playbook playbooks/test-connections.yml

# Gather facts
ansible-playbook playbooks/gather-facts.yml
```

## What's New in V2

### ✅ Improved Structure
- Modular role-based architecture
- Separate playbooks for different functions
- Better organization of files and templates

### ✅ Enhanced Error Handling
- Comprehensive fallback mechanisms
- Detailed error reporting
- Connection validation and testing

### ✅ AWX Integration
- Ready-to-use AWX configuration
- Proper credential management
- Workflow templates included

### ✅ Better Reporting
- HTML test reports
- Detailed connection summaries
- Comprehensive logging

### ✅ Flexible Configuration
- Customizable user lists
- Configurable timeouts
- Environment-specific settings

## Directory Structure
```
lab-v2/
├── playbooks/           # Main playbooks
│   ├── gather-facts.yml
│   ├── test-connections.yml
│   └── setup-environment.yml
├── roles/              # Ansible roles
│   ├── ssh-setup/      # SSH configuration
│   ├── fact-gathering/ # Fact collection with fallback
│   └── connection-test/ # Connection testing utilities
├── inventory/          # Inventory files
│   ├── development.yml
│   └── production.yml
├── group_vars/         # Global variables
└── templates/          # Report templates
```

## Common Use Cases

### 1. Initial Environment Setup
```bash
ansible-playbook playbooks/setup-environment.yml
```

### 2. Daily Fact Gathering
```bash
ansible-playbook playbooks/gather-facts.yml -i inventory/production.yml
```

### 3. Connection Troubleshooting
```bash
ansible-playbook playbooks/test-connections.yml --extra-vars "detailed_output=true"
```

### 4. Specific Host Testing
```bash
ansible-playbook playbooks/test-connections.yml --limit "ubuntu_hosts"
```

## Configuration Examples

### Custom User Priority
```yaml
# group_vars/all.yml
ssh_users:
  - my-custom-user
  - ubuntu
  - centos
  - cloud-user
```

### Extended Timeouts
```yaml
# For slow networks
connection_timeout: 60
gather_facts_timeout: 120
```

### Debug Mode
```yaml
enable_debug: true
log_level: DEBUG
```

## Troubleshooting

### Connection Failures
1. Check SSH keys: `ls -la roles/ssh-setup/files/`
2. Verify inventory: `ansible-inventory --list`
3. Test manually: `ssh -i key.pem user@host`

### Permission Issues
```bash
# Fix SSH key permissions
find roles/ssh-setup/files/ -name "*.pem" -exec chmod 400 {} \;
```

### AWX Issues
- Check credential configuration
- Verify project sync
- Review job logs in AWX interface

## Next Steps

1. **For Development**: Use `inventory/development.yml`
2. **For Production**: Use `inventory/production.yml`
3. **For AWX**: Follow `AWX_SETUP.md`
4. **For Customization**: Modify `group_vars/all.yml`

## Support

- Check logs in `/tmp/` for detailed reports
- Review connection test HTML reports
- Use debug mode for troubleshooting
- Refer to AWX_SETUP.md for AWX-specific issues
