# Lab V2 - Enhanced SSH Multiple User Management

## Overview
Improved version of SSH multiple user management with better structure, error handling, and AWX integration.

## Structure
```
lab-v2/
├── playbooks/
│   ├── gather-facts.yml          # Main gather facts playbook
│   ├── test-connections.yml      # Test connections with dynamic users
│   └── setup-environment.yml     # Setup SSH environment
├── roles/
│   ├── ssh-setup/               # Enhanced SSH configuration
│   ├── fact-gathering/          # Modular fact gathering
│   └── connection-test/         # Connection testing utilities
├── group_vars/
│   └── all.yml                  # Global variables
├── host_vars/                   # Host-specific variables
├── inventory/
│   ├── development.yml          # Development inventory
│   └── production.yml           # Production inventory
└── ansible.cfg                 # Ansible configuration
```

## Features
- Modular role-based architecture
- Enhanced error handling and logging
- AWX-ready configuration
- Support for multiple SSH users (ubuntu, centos, cloud-user, ec2-user)
- Fallback mechanisms for different cloud providers
- Comprehensive testing utilities

## Usage

### 1. Gather Facts
```bash
ansible-playbook -i inventory/development.yml playbooks/gather-facts.yml
```

### 2. Test Connections
```bash
ansible-playbook -i inventory/development.yml playbooks/test-connections.yml
```

### 3. Setup Environment
```bash
ansible-playbook -i inventory/development.yml playbooks/setup-environment.yml
```

## AWX Integration
- All playbooks are designed to work with AWX/Tower
- SSH keys are managed through the ssh-setup role
- Inventory can be imported directly into AWX
- Job templates can be created for each playbook
