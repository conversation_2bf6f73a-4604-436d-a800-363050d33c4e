---
# Global variables for all hosts

# SSH Configuration
ssh_users:
  - ubuntu
  - centos
  - cloud-user
  - ec2-user
  - admin
  - root

# SSH Keys
ssh_keys:
  - name: controller
    file: controller.pem
  - name: labms
    file: labms.pem

# Connection settings
connection_timeout: 30
gather_facts_timeout: 60

# Logging
log_level: INFO
enable_debug: false

# AWX/Tower specific settings
awx_runner_path: /runner
awx_ssh_path: "{{ awx_runner_path }}/.ssh"

# Fallback settings
max_connection_retries: 3
retry_delay: 5

# OS Detection
supported_os_families:
  - RedHat
  - Debian
  - Ubuntu
  - CentOS
  - Amazon

# Default users per OS
default_users:
  ubuntu: ubuntu
  centos: centos
  rhel: cloud-user
  amazon: ec2-user
  debian: admin
