---
# Example host-specific variables
# Copy this file and rename it to match your hostname

# Host-specific SSH configuration
preferred_user: ubuntu
ssh_port: 22

# Custom SSH users for this host (overrides global)
ssh_users:
  - ubuntu
  - custom-user

# Connection settings
connection_timeout: 45
gather_facts_timeout: 90

# Host-specific SSH key (if different from global)
# ssh_key_files:
#   - custom-host-key.pem

# Environment-specific settings
environment: development
role: web-server

# Custom variables for this host
custom_config:
  enable_monitoring: true
  backup_enabled: false
