---
all:
  children:
    development:
      children:
        ubuntu_hosts:
          hosts:
            ubuntu-dev-01:
              ansible_host: ************
              preferred_user: ubuntu
            ubuntu-dev-02:
              ansible_host: ************
              preferred_user: ubuntu
        
        centos_hosts:
          hosts:
            centos-dev-01:
              ansible_host: ************
              preferred_user: centos
            centos-dev-02:
              ansible_host: ************
              preferred_user: centos
        
        rhel_hosts:
          hosts:
            rhel-dev-01:
              ansible_host: ************
              preferred_user: cloud-user
            rhel-dev-02:
              ansible_host: ************
              preferred_user: cloud-user
        
        amazon_hosts:
          hosts:
            amazon-dev-01:
              ansible_host: ************
              preferred_user: ec2-user
            amazon-dev-02:
              ansible_host: ************
              preferred_user: ec2-user

  vars:
    environment: development
    ansible_ssh_common_args: '-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null'
