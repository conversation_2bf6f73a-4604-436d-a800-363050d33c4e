---
all:
  children:
    production:
      children:
        web_servers:
          hosts:
            web-prod-01:
              ansible_host: *********
              preferred_user: ubuntu
            web-prod-02:
              ansible_host: *********
              preferred_user: ubuntu
        
        app_servers:
          hosts:
            app-prod-01:
              ansible_host: *********
              preferred_user: centos
            app-prod-02:
              ansible_host: *********
              preferred_user: centos
        
        db_servers:
          hosts:
            db-prod-01:
              ansible_host: *********
              preferred_user: cloud-user
            db-prod-02:
              ansible_host: *********
              preferred_user: cloud-user
        
        load_balancers:
          hosts:
            lb-prod-01:
              ansible_host: *********
              preferred_user: ec2-user
            lb-prod-02:
              ansible_host: *********
              preferred_user: ec2-user

  vars:
    environment: production
    ansible_ssh_common_args: '-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null'
