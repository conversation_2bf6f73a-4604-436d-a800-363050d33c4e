---
# Enhanced <PERSON>ather Facts Playbook
# This playbook sets up SSH configuration and gathers facts with fallback mechanisms

- name: Setup SSH Configuration
  hosts: localhost
  gather_facts: false
  become: false
  tags:
    - setup
    - ssh
  roles:
    - ssh-setup

- name: Gather Facts with Multiple User Fallback
  hosts: all
  gather_facts: false
  ignore_unreachable: true
  serial: "{{ ansible_serial | default('100%') }}"
  tags:
    - facts
    - gathering
  vars:
    # Override default timeout for fact gathering
    ansible_timeout: "{{ gather_facts_timeout | default(60) }}"
  
  pre_tasks:
    - name: Display target host information
      debug:
        msg: |
          Starting fact gathering for: {{ inventory_hostname }}
          Preferred user: {{ preferred_user | default('auto-detect') }}
          Available SSH users: {{ ssh_users | join(', ') }}
      run_once: false
      tags: always

  roles:
    - role: fact-gathering
      tags:
        - facts
        - connection

  post_tasks:
    - name: Validate gathered facts
      assert:
        that:
          - ansible_hostname is defined
          - ansible_os_family is defined
        fail_msg: "Critical facts were not gathered successfully"
        success_msg: "Facts gathered successfully for {{ inventory_hostname }}"
      tags: validation

    - name: Display gathered system information
      debug:
        msg: |
          === SYSTEM INFORMATION ===
          Hostname: {{ ansible_hostname }}
          FQDN: {{ ansible_fqdn | default('N/A') }}
          OS Family: {{ ansible_os_family }}
          Distribution: {{ ansible_distribution }} {{ ansible_distribution_version }}
          Architecture: {{ ansible_architecture }}
          Kernel: {{ ansible_kernel }}
          Memory: {{ ansible_memtotal_mb }}MB
          CPU Cores: {{ ansible_processor_vcpus }}
          Successful User: {{ successful_user | default('N/A') }}
          Connection Method: {{ connection_method | default('N/A') }}
          ==========================
      when: 
        - ansible_hostname is defined
        - log_level == 'INFO' or enable_debug
      tags: info

    - name: Save host facts to local file (optional)
      copy:
        content: |
          # Host Facts for {{ inventory_hostname }}
          # Generated: {{ ansible_date_time.iso8601 }}
          
          hostname: {{ ansible_hostname }}
          fqdn: {{ ansible_fqdn | default('N/A') }}
          os_family: {{ ansible_os_family }}
          distribution: {{ ansible_distribution }}
          distribution_version: {{ ansible_distribution_version }}
          architecture: {{ ansible_architecture }}
          kernel: {{ ansible_kernel }}
          memory_mb: {{ ansible_memtotal_mb }}
          cpu_cores: {{ ansible_processor_vcpus }}
          successful_user: {{ successful_user | default('N/A') }}
          connection_method: {{ connection_method | default('N/A') }}
          ip_addresses: {{ ansible_all_ipv4_addresses | default([]) | to_yaml }}
        dest: "/tmp/{{ inventory_hostname }}_facts.yml"
      delegate_to: localhost
      when: save_facts_to_file | default(false)
      tags: save

  handlers:
    - name: Clear fact cache
      meta: clear_facts
      listen: "clear cache"
