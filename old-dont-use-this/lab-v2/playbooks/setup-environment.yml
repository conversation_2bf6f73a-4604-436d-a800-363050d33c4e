---
# Environment Setup Playbook
# This playbook sets up the complete environment for SSH multiple user management

- name: Setup SSH Configuration and Environment
  hosts: localhost
  gather_facts: true
  become: false
  tags:
    - setup
    - environment
  vars:
    setup_validation: true
    
  tasks:
    - name: Display setup information
      debug:
        msg: |
          === ENVIRONMENT SETUP ===
          AWX Runner Path: {{ awx_runner_path | default('/home/' + ansible_user) }}
          SSH Base Path: {{ awx_ssh_path | default(awx_runner_path + '/.ssh') }}
          SSH Users: {{ ssh_users | join(', ') }}
          SSH Keys: {{ ssh_keys | map(attribute='name') | join(', ') }}
          ==========================

    - name: Include SSH setup role
      include_role:
        name: ssh-setup
      tags: ssh

    - name: Validate SSH setup
      block:
        - name: Check SSH directory
          stat:
            path: "{{ awx_ssh_path | default('/runner/.ssh') }}"
          register: ssh_dir_stat

        - name: Check SSH config file
          stat:
            path: "{{ awx_ssh_path | default('/runner/.ssh') }}/config"
          register: ssh_config_stat

        - name: Check SSH key files
          stat:
            path: "{{ awx_ssh_path | default('/runner/.ssh') }}/{{ item.file }}"
          register: ssh_key_stats
          loop: "{{ ssh_keys }}"

        - name: Validate setup results
          assert:
            that:
              - ssh_dir_stat.stat.exists
              - ssh_dir_stat.stat.isdir
              - ssh_config_stat.stat.exists
              - ssh_key_stats.results | selectattr('stat.exists') | list | length == ssh_keys | length
            fail_msg: "SSH setup validation failed"
            success_msg: "SSH setup validation passed"

      when: setup_validation
      tags: validation

    - name: Display setup completion
      debug:
        msg: |
          === SETUP COMPLETE ===
          ✅ SSH directory created
          ✅ SSH config generated
          ✅ SSH keys installed
          ✅ Permissions set correctly
          
          Next steps:
          1. Update inventory files with your hosts
          2. Copy actual SSH private keys to replace placeholders
          3. Run gather-facts.yml to test connections
          4. Run test-connections.yml for comprehensive testing
          ======================

- name: Validate Environment with Sample Hosts
  hosts: all
  gather_facts: false
  ignore_unreachable: true
  tags:
    - validate
    - test
  vars:
    quick_test: true
    
  tasks:
    - name: Quick connection test
      ping:
      register: ping_result
      ignore_errors: true

    - name: Display connection status
      debug:
        msg: |
          Host: {{ inventory_hostname }}
          Status: {{ 'REACHABLE' if ping_result is succeeded else 'UNREACHABLE' }}
          {% if ping_result is failed %}
          Error: {{ ping_result.msg | default('Unknown error') }}
          {% endif %}
      when: quick_test

- name: Generate Setup Report
  hosts: localhost
  gather_facts: false
  tags:
    - report
    - summary
  tasks:
    - name: Collect ping results
      set_fact:
        ping_results: "{{ groups['all'] | map('extract', hostvars, 'ping_result') | select('defined') | list }}"

    - name: Generate setup report
      copy:
        content: |
          # Environment Setup Report
          # Generated: {{ ansible_date_time.iso8601 }}
          
          ## SSH Configuration
          - SSH Directory: {{ awx_ssh_path | default('/runner/.ssh') }}
          - SSH Config: {{ awx_ssh_path | default('/runner/.ssh') }}/config
          - SSH Keys: {{ ssh_keys | map(attribute='file') | join(', ') }}
          
          ## Inventory Summary
          - Total Hosts: {{ groups['all'] | length }}
          - Host Groups: {{ groups.keys() | reject('equalto', 'all') | reject('equalto', 'ungrouped') | list | join(', ') }}
          
          ## Quick Connection Test Results
          {% for host in groups['all'] %}
          {% set result = hostvars[host].ping_result | default({'failed': true, 'msg': 'No test performed'}) %}
          - {{ host }}: {{ 'PASS' if not result.failed else 'FAIL - ' + result.msg }}
          {% endfor %}
          
          ## Next Steps
          1. Replace placeholder SSH keys with actual private keys
          2. Update inventory with real host information
          3. Run full fact gathering: ansible-playbook playbooks/gather-facts.yml
          4. Run connection tests: ansible-playbook playbooks/test-connections.yml
        dest: "/tmp/environment_setup_report.md"
      tags: save
