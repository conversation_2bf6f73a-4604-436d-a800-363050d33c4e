---
# Connection Testing Playbook
# This playbook tests connections with multiple users and generates comprehensive reports

- name: Setup SSH Configuration
  hosts: localhost
  gather_facts: false
  become: false
  tags:
    - setup
    - ssh
  roles:
    - ssh-setup

- name: Test Connections with Multiple Users
  hosts: all
  gather_facts: false
  ignore_unreachable: true
  serial: "{{ ansible_serial | default('100%') }}"
  tags:
    - test
    - connection
  vars:
    # Test configuration
    test_timeout: "{{ connection_test_timeout | default(30) }}"
    detailed_output: true
    save_test_results: true
    generate_report: true
  
  pre_tasks:
    - name: Display test configuration
      debug:
        msg: |
          Starting connection tests for: {{ inventory_hostname }}
          Test users: {{ test_users | default(ssh_users) | join(', ') }}
          Test timeout: {{ test_timeout }}s
          Detailed output: {{ detailed_output }}
      run_once: false
      tags: always

  roles:
    - role: connection-test
      tags:
        - test
        - validation

  post_tasks:
    - name: Generate host-specific test report
      template:
        src: connection_test_report.j2
        dest: "/tmp/{{ inventory_hostname }}_connection_test.html"
      delegate_to: localhost
      when: generate_report and connection_test_results is defined
      tags: report

    - name: Display connection recommendations
      debug:
        msg: |
          === CONNECTION RECOMMENDATIONS ===
          Host: {{ inventory_hostname }}
          {% if connection_test_results.overall_status == 'success' %}
          Status: ✅ PASSED
          Recommended User: {{ connection_test_results.detailed_results | selectattr('status', 'equalto', 'success') | map(attribute='user') | first }}
          Available Users: {{ connection_test_results.detailed_results | selectattr('status', 'equalto', 'success') | map(attribute='user') | join(', ') }}
          {% else %}
          Status: ❌ FAILED
          Issue: No successful connections found
          Troubleshooting: Check SSH keys, network connectivity, and user permissions
          {% endif %}
          ===================================
      when: connection_test_results is defined
      tags: summary

- name: Generate Overall Test Summary
  hosts: localhost
  gather_facts: false
  tags:
    - summary
    - report
  tasks:
    - name: Collect all test results
      set_fact:
        all_test_results: "{{ groups['all'] | map('extract', hostvars, 'connection_test_results') | select('defined') | list }}"

    - name: Calculate overall statistics
      set_fact:
        total_hosts: "{{ groups['all'] | length }}"
        successful_hosts: "{{ all_test_results | selectattr('overall_status', 'equalto', 'success') | list | length }}"
        failed_hosts: "{{ all_test_results | selectattr('overall_status', 'equalto', 'failed') | list | length }}"

    - name: Display overall summary
      debug:
        msg: |
          === OVERALL TEST SUMMARY ===
          Total Hosts: {{ total_hosts }}
          Successful: {{ successful_hosts }}
          Failed: {{ failed_hosts }}
          Success Rate: {{ ((successful_hosts | int) / (total_hosts | int) * 100) | round(2) }}%
          
          {% for result in all_test_results %}
          {{ result.host }}: {{ result.overall_status | upper }}
          {% endfor %}
          ============================
      when: all_test_results | length > 0

    - name: Save overall summary to file
      copy:
        content: |
          # Connection Test Summary
          # Generated: {{ ansible_date_time.iso8601 }}
          
          total_hosts: {{ total_hosts }}
          successful_hosts: {{ successful_hosts }}
          failed_hosts: {{ failed_hosts }}
          success_rate: {{ ((successful_hosts | int) / (total_hosts | int) * 100) | round(2) }}%
          
          detailed_results:
          {% for result in all_test_results %}
            - host: {{ result.host }}
              status: {{ result.overall_status }}
              timestamp: {{ result.timestamp }}
          {% endfor %}
        dest: "/tmp/connection_test_summary.yml"
      when: all_test_results | length > 0
      tags: save
