---
# Default variables for connection-test role

# Test commands to run
test_commands:
  - name: "Basic connectivity"
    command: "echo 'Connection successful'"
    expected_output: "Connection successful"
  - name: "System information"
    command: "uname -a"
    expected_pattern: "Linux"
  - name: "Network routing"
    command: "ip route | grep default || route -n | grep '^0.0.0.0'"
    expected_pattern: "default|0.0.0.0"
  - name: "Disk space"
    command: "df -h /"
    expected_pattern: "/"
  - name: "Memory information"
    command: "free -m"
    expected_pattern: "Mem:"

# Users to test
test_users:
  - "{{ ansible_user | default('ubuntu') }}"
  - ubuntu
  - centos
  - cloud-user
  - ec2-user

# Test settings
run_all_tests: true
stop_on_first_success: false
test_timeout: 30
parallel_testing: false

# Output settings
detailed_output: true
save_test_results: true
generate_report: true

# Privilege testing
test_sudo_access: true
sudo_test_command: "sudo whoami"
