---
# Generate test summary and report

- name: Calculate test statistics
  set_fact:
    total_users_tested: "{{ test_results | length }}"
    successful_users: "{{ test_results | selectattr('status', 'equalto', 'success') | list | length }}"
    failed_users: "{{ test_results | selectattr('status', 'equalto', 'failed') | list | length }}"
    users_with_sudo: "{{ test_results | selectattr('sudo_access', 'defined') | selectattr('sudo_access', 'equalto', true) | list | length }}"

- name: Generate summary report
  set_fact:
    test_summary:
      host: "{{ inventory_hostname }}"
      timestamp: "{{ ansible_date_time.iso8601 }}"
      statistics:
        total_users_tested: "{{ total_users_tested }}"
        successful_connections: "{{ successful_users }}"
        failed_connections: "{{ failed_users }}"
        users_with_sudo: "{{ users_with_sudo }}"
        success_rate: "{{ ((successful_users | int) / (total_users_tested | int) * 100) | round(2) }}%"
      recommended_user: "{{ (test_results | selectattr('status', 'equalto', 'success') | list | first).user | default('none') }}"
      all_results: "{{ test_results }}"

- name: Display summary report
  debug:
    msg: |
      === CONNECTION TEST SUMMARY ===
      Host: {{ test_summary.host }}
      Timestamp: {{ test_summary.timestamp }}
      
      Statistics:
      - Total Users Tested: {{ test_summary.statistics.total_users_tested }}
      - Successful Connections: {{ test_summary.statistics.successful_connections }}
      - Failed Connections: {{ test_summary.statistics.failed_connections }}
      - Users with Sudo Access: {{ test_summary.statistics.users_with_sudo }}
      - Success Rate: {{ test_summary.statistics.success_rate }}
      
      Recommended User: {{ test_summary.recommended_user }}
      ================================
