---
# Main tasks for connection-test role

- name: Initialize test results
  set_fact:
    test_results: []
    overall_status: "unknown"

- name: Run connection tests for each user
  include_tasks: test_user_connection.yml
  loop: "{{ test_users | unique }}"
  loop_control:
    loop_var: test_user
  when: test_user != ""

- name: Test sudo access if enabled
  include_tasks: test_sudo_access.yml
  when: test_sudo_access

- name: Generate test summary
  include_tasks: generate_summary.yml
  when: generate_report

- name: Display test results
  debug:
    msg: |
      Connection Test Results for {{ inventory_hostname }}:
      {% for result in test_results %}
      - User: {{ result.user }}
        Status: {{ result.status }}
        {% if result.status == 'success' %}
        Successful Tests: {{ result.successful_tests | length }}
        {% else %}
        Failed Tests: {{ result.failed_tests | length }}
        {% endif %}
      {% endfor %}
      Overall Status: {{ overall_status }}
  when: detailed_output

- name: Save test results to facts
  set_fact:
    connection_test_results:
      timestamp: "{{ ansible_date_time.iso8601 }}"
      host: "{{ inventory_hostname }}"
      overall_status: "{{ overall_status }}"
      detailed_results: "{{ test_results }}"
  when: save_test_results
