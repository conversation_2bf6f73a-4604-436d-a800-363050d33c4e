---
# Test sudo access for successful connections

- name: Test sudo access for successful users
  block:
    - name: "Test sudo access"
      shell: "{{ sudo_test_command }}"
      remote_user: "{{ item.user }}"
      become: true
      register: sudo_test_result
      loop: "{{ test_results | selectattr('status', 'equalto', 'success') | list }}"
      ignore_errors: true

    - name: Update test results with sudo information
      set_fact:
        test_results: "{{ test_results | map('combine', {'sudo_access': (item.rc == 0)}) | list }}"
      loop: "{{ sudo_test_result.results }}"
      when: sudo_test_result.results is defined

  rescue:
    - name: Mark sudo test as failed
      set_fact:
        test_results: "{{ test_results | map('combine', {'sudo_access': false, 'sudo_error': ansible_failed_result.msg | default('Sudo test failed')}) | list }}"
