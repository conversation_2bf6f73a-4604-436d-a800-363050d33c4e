---
# Test connection for a specific user

- name: "Initialize test results for {{ test_user }}"
  set_fact:
    user_test_result:
      user: "{{ test_user }}"
      status: "unknown"
      successful_tests: []
      failed_tests: []
      connection_method: "unknown"

- name: "Test connection as {{ test_user }}"
  block:
    - name: "Run test commands as {{ test_user }}"
      shell: "{{ item.command }}"
      remote_user: "{{ test_user }}"
      register: command_result
      loop: "{{ test_commands }}"
      loop_control:
        label: "{{ item.name }}"
      ignore_errors: true
      timeout: "{{ test_timeout }}"

    - name: "Evaluate test results for {{ test_user }}"
      set_fact:
        user_test_result: "{{ user_test_result | combine({
          'status': 'success',
          'connection_method': 'direct',
          'successful_tests': user_test_result.successful_tests + [item.item.name],
          'test_outputs': user_test_result.test_outputs | default({}) | combine({item.item.name: item.stdout})
        }) }}"
      loop: "{{ command_result.results }}"
      when: 
        - item.rc == 0
        - (item.item.expected_output is not defined) or (item.item.expected_output in item.stdout)
        - (item.item.expected_pattern is not defined) or (item.stdout | regex_search(item.item.expected_pattern))

    - name: "Record failed tests for {{ test_user }}"
      set_fact:
        user_test_result: "{{ user_test_result | combine({
          'failed_tests': user_test_result.failed_tests + [item.item.name],
          'error_details': user_test_result.error_details | default({}) | combine({item.item.name: item.stderr | default('Command failed')})
        }) }}"
      loop: "{{ command_result.results }}"
      when: 
        - item.rc != 0 or
          (item.item.expected_output is defined and item.item.expected_output not in item.stdout) or
          (item.item.expected_pattern is defined and not (item.stdout | regex_search(item.item.expected_pattern)))

  rescue:
    - name: "Mark connection failed for {{ test_user }}"
      set_fact:
        user_test_result: "{{ user_test_result | combine({
          'status': 'failed',
          'connection_method': 'none',
          'error': ansible_failed_result.msg | default('Connection failed')
        }) }}"

- name: "Update overall test results"
  set_fact:
    test_results: "{{ test_results + [user_test_result] }}"

- name: "Update overall status"
  set_fact:
    overall_status: "{{ 'success' if (test_results | selectattr('status', 'equalto', 'success') | list | length > 0) else 'failed' }}"
