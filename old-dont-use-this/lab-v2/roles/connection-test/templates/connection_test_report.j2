<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connection Test Report - {{ inventory_hostname }}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
        .status-success { color: #28a745; font-weight: bold; }
        .status-failed { color: #dc3545; font-weight: bold; }
        .status-unknown { color: #6c757d; font-weight: bold; }
        .summary-box { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .test-result { margin: 15px 0; padding: 15px; border-radius: 5px; }
        .test-success { background: #d4edda; border-left: 4px solid #28a745; }
        .test-failed { background: #f8d7da; border-left: 4px solid #dc3545; }
        .test-details { margin-top: 10px; font-size: 0.9em; }
        .command-output { background: #f1f1f1; padding: 10px; border-radius: 3px; font-family: monospace; margin: 5px 0; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .timestamp { color: #6c757d; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Connection Test Report</h1>
            <h2>{{ inventory_hostname }}</h2>
            <p class="timestamp">Generated: {{ ansible_date_time.iso8601 }}</p>
        </div>

        <div class="summary-box">
            <h3>Test Summary</h3>
            <p><strong>Overall Status:</strong> 
                <span class="status-{{ connection_test_results.overall_status }}">
                    {{ connection_test_results.overall_status | upper }}
                </span>
            </p>
            <p><strong>Total Users Tested:</strong> {{ connection_test_results.detailed_results | length }}</p>
            <p><strong>Successful Connections:</strong> {{ connection_test_results.detailed_results | selectattr('status', 'equalto', 'success') | list | length }}</p>
            <p><strong>Failed Connections:</strong> {{ connection_test_results.detailed_results | selectattr('status', 'equalto', 'failed') | list | length }}</p>
            {% if connection_test_results.detailed_results | selectattr('status', 'equalto', 'success') | list | length > 0 %}
            <p><strong>Recommended User:</strong> {{ (connection_test_results.detailed_results | selectattr('status', 'equalto', 'success') | list | first).user }}</p>
            {% endif %}
        </div>

        <h3>Detailed Test Results</h3>
        {% for result in connection_test_results.detailed_results %}
        <div class="test-result test-{{ result.status }}">
            <h4>User: {{ result.user }}</h4>
            <p><strong>Status:</strong> <span class="status-{{ result.status }}">{{ result.status | upper }}</span></p>
            <p><strong>Connection Method:</strong> {{ result.connection_method }}</p>
            
            {% if result.sudo_access is defined %}
            <p><strong>Sudo Access:</strong> {{ 'Yes' if result.sudo_access else 'No' }}</p>
            {% endif %}

            {% if result.successful_tests is defined and result.successful_tests | length > 0 %}
            <div class="test-details">
                <strong>Successful Tests ({{ result.successful_tests | length }}):</strong>
                <ul>
                {% for test in result.successful_tests %}
                    <li>{{ test }}</li>
                {% endfor %}
                </ul>
            </div>
            {% endif %}

            {% if result.failed_tests is defined and result.failed_tests | length > 0 %}
            <div class="test-details">
                <strong>Failed Tests ({{ result.failed_tests | length }}):</strong>
                <ul>
                {% for test in result.failed_tests %}
                    <li>{{ test }}</li>
                {% endfor %}
                </ul>
            </div>
            {% endif %}

            {% if result.test_outputs is defined %}
            <div class="test-details">
                <strong>Command Outputs:</strong>
                {% for test_name, output in result.test_outputs.items() %}
                <div>
                    <strong>{{ test_name }}:</strong>
                    <div class="command-output">{{ output }}</div>
                </div>
                {% endfor %}
            </div>
            {% endif %}

            {% if result.error is defined %}
            <div class="test-details">
                <strong>Error:</strong> {{ result.error }}
            </div>
            {% endif %}
        </div>
        {% endfor %}

        <div class="summary-box">
            <h3>Recommendations</h3>
            {% if connection_test_results.overall_status == 'success' %}
            <p>✅ Connection tests passed successfully!</p>
            <p><strong>Recommended configuration:</strong></p>
            <ul>
                <li>Use user: <code>{{ (connection_test_results.detailed_results | selectattr('status', 'equalto', 'success') | list | first).user }}</code></li>
                <li>Connection method: {{ (connection_test_results.detailed_results | selectattr('status', 'equalto', 'success') | list | first).connection_method }}</li>
                {% if (connection_test_results.detailed_results | selectattr('status', 'equalto', 'success') | selectattr('sudo_access', 'equalto', true) | list | length > 0) %}
                <li>Sudo access: Available</li>
                {% endif %}
            </ul>
            {% else %}
            <p>❌ All connection tests failed.</p>
            <p><strong>Troubleshooting steps:</strong></p>
            <ul>
                <li>Verify SSH keys are correctly installed</li>
                <li>Check network connectivity to the host</li>
                <li>Ensure the target users exist on the system</li>
                <li>Verify SSH service is running on the target host</li>
                <li>Check firewall rules and security groups</li>
            </ul>
            {% endif %}
        </div>
    </div>
</body>
</html>
