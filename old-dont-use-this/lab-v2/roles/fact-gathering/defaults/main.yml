---
# Default variables for fact-gathering role

# User priority order for fact gathering
fact_gathering_users:
  - "{{ preferred_user | default('') }}"
  - ubuntu
  - centos
  - cloud-user
  - ec2-user
  - admin
  - root

# Timeout settings
fact_gathering_timeout: 60
connection_retry_delay: 5
max_retries: 3

# Privilege escalation settings
try_become_first: true
fallback_without_become: true

# Logging and debugging
log_connection_attempts: true
save_connection_results: true

# Facts to gather (can be customized)
gather_subset:
  - "!all"
  - "!any"
  - network
  - hardware
  - virtual
  - distribution

# Connection validation
validate_connection: true
test_command: "echo 'Connection successful'"
