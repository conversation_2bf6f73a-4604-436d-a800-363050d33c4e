---
# Attempt connection with a specific user

- name: "Attempt fact gathering as {{ current_user }} with become"
  block:
    - name: "Gather facts as {{ current_user }} (with become)"
      setup:
        gather_subset: "{{ gather_subset }}"
        gather_timeout: "{{ fact_gathering_timeout }}"
      remote_user: "{{ current_user }}"
      become: true
      register: fact_result_become
      
    - name: "Test connection as {{ current_user }} (with become)"
      command: "{{ test_command }}"
      remote_user: "{{ current_user }}"
      become: true
      register: test_result_become
      when: validate_connection
      
    - name: "Mark successful connection with become"
      set_fact:
        successful_user: "{{ current_user }}"
        facts_gathered: true
        successful_with_become: true
        connection_attempts: "{{ connection_attempts + [{'user': current_user, 'method': 'become', 'status': 'success'}] }}"
      
  rescue:
    - name: "Log failed attempt with become"
      set_fact:
        connection_attempts: "{{ connection_attempts + [{'user': current_user, 'method': 'become', 'status': 'failed', 'error': ansible_failed_result.msg | default('Unknown error')}] }}"
      
    - name: "Attempt fact gathering as {{ current_user }} without become"
      block:
        - name: "Gather facts as {{ current_user }} (without become)"
          setup:
            gather_subset: "{{ gather_subset }}"
            gather_timeout: "{{ fact_gathering_timeout }}"
          remote_user: "{{ current_user }}"
          become: false
          register: fact_result_no_become
          
        - name: "Test connection as {{ current_user }} (without become)"
          command: "{{ test_command }}"
          remote_user: "{{ current_user }}"
          become: false
          register: test_result_no_become
          when: validate_connection
          
        - name: "Mark successful connection without become"
          set_fact:
            successful_user: "{{ current_user }}"
            facts_gathered: true
            successful_with_become: false
            connection_attempts: "{{ connection_attempts + [{'user': current_user, 'method': 'no_become', 'status': 'success'}] }}"
            
      rescue:
        - name: "Log failed attempt without become"
          set_fact:
            connection_attempts: "{{ connection_attempts + [{'user': current_user, 'method': 'no_become', 'status': 'failed', 'error': ansible_failed_result.msg | default('Unknown error')}] }}"
          
      when: fallback_without_become and not facts_gathered
      
  when: try_become_first and not facts_gathered
