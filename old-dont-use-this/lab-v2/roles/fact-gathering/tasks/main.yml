---
# Main tasks for fact-gathering role

- name: Initialize connection tracking variables
  set_fact:
    successful_user: ""
    connection_attempts: []
    facts_gathered: false

- name: Attempt fact gathering with each user
  include_tasks: attempt_connection.yml
  loop: "{{ fact_gathering_users | select('ne', '') | list }}"
  loop_control:
    loop_var: current_user
  when: not facts_gathered

- name: Validate successful connection
  fail:
    msg: "Failed to gather facts with any available user: {{ fact_gathering_users | join(', ') }}"
  when: not facts_gathered

- name: Set final connection user
  set_fact:
    ansible_user: "{{ successful_user }}"
    connection_method: "{{ 'become' if successful_with_become | default(false) else 'direct' }}"
  when: facts_gathered

- name: Display connection summary
  debug:
    msg: |
      Fact Gathering Summary:
      - Successful User: {{ successful_user }}
      - Connection Method: {{ connection_method | default('unknown') }}
      - Host: {{ inventory_hostname }}
      - OS Family: {{ ansible_os_family | default('unknown') }}
      - Distribution: {{ ansible_distribution | default('unknown') }} {{ ansible_distribution_version | default('') }}
  when: facts_gathered and log_connection_attempts

- name: Save connection results to host vars
  set_fact:
    host_connection_info:
      successful_user: "{{ successful_user }}"
      connection_method: "{{ connection_method | default('unknown') }}"
      attempts: "{{ connection_attempts }}"
      timestamp: "{{ ansible_date_time.iso8601 }}"
  when: save_connection_results and facts_gathered
