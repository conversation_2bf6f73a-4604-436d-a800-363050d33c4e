---
# Default variables for ssh-setup role

# SSH directory paths
ssh_base_path: "{{ awx_runner_path | default('/home/' + ansible_user) }}/.ssh"
ssh_config_file: "{{ ssh_base_path }}/config"

# SSH key files
ssh_key_files:
  - controller.pem
  - labms.pem

# SSH configuration template
ssh_config_template: |
  Host *
    StrictHostKeyChecking no
    UserKnownHostsFile /dev/null
    ServerAliveInterval 60
    ServerAliveCountMax 3
    ConnectTimeout {{ connection_timeout | default(30) }}
    {% for key_file in ssh_key_files %}
    IdentityFile {{ ssh_base_path }}/{{ key_file }}
    {% endfor %}

# File permissions
ssh_dir_mode: '0700'
ssh_key_mode: '0400'
ssh_config_mode: '0600'

# Backup settings
backup_ssh_config: true
backup_suffix: '.backup'
