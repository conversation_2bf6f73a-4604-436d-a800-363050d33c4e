---
# Main tasks for ssh-setup role

- name: Include OS-specific variables
  include_vars: "{{ ansible_os_family }}.yml"
  ignore_errors: true

- name: Ensure SSH directory exists
  file:
    path: "{{ ssh_base_path }}"
    state: directory
    mode: "{{ ssh_dir_mode }}"
    owner: "{{ ansible_user | default('runner') }}"
  delegate_to: localhost
  run_once: true

- name: Copy SSH private keys
  copy:
    src: "{{ item }}"
    dest: "{{ ssh_base_path }}/{{ item }}"
    mode: "{{ ssh_key_mode }}"
    owner: "{{ ansible_user | default('runner') }}"
    backup: "{{ backup_ssh_config }}"
  loop: "{{ ssh_key_files }}"
  delegate_to: localhost
  run_once: true
  no_log: true

- name: Generate SSH config from template
  template:
    src: ssh_config.j2
    dest: "{{ ssh_config_file }}"
    mode: "{{ ssh_config_mode }}"
    owner: "{{ ansible_user | default('runner') }}"
    backup: "{{ backup_ssh_config }}"
  delegate_to: localhost
  run_once: true

- name: Verify SSH key files exist
  stat:
    path: "{{ ssh_base_path }}/{{ item }}"
  register: ssh_key_stat
  loop: "{{ ssh_key_files }}"
  delegate_to: localhost
  run_once: true

- name: Display SSH setup status
  debug:
    msg: |
      SSH Setup Complete:
      - SSH Directory: {{ ssh_base_path }}
      - Config File: {{ ssh_config_file }}
      - Keys Installed: {{ ssh_key_files | join(', ') }}
  run_once: true
