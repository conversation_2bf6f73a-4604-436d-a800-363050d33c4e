# SSH Configuration for Multiple User Access
# Generated by Ansible - {{ ansible_date_time.iso8601 }}

Host *
    # Security settings
    StrictHostKeyChecking no
    UserKnownHostsFile /dev/null
    
    # Connection settings
    ServerAliveInterval 60
    ServerAliveCountMax 3
    ConnectTimeout {{ connection_timeout | default(30) }}
    
    # Performance settings
    ControlMaster auto
    ControlPath /tmp/ansible-ssh-%h-%p-%r
    ControlPersist 60s
    
    # Identity files
{% for key_file in ssh_key_files %}
    IdentityFile {{ ssh_base_path }}/{{ key_file }}
{% endfor %}
    
    # Logging (for debugging)
{% if enable_debug | default(false) %}
    LogLevel DEBUG
{% else %}
    LogLevel ERROR
{% endif %}

# Host-specific configurations can be added below
# Example:
# Host specific-host
#     HostName *************
#     User ubuntu
#     IdentityFile {{ ssh_base_path }}/specific-key.pem
