#!/bin/bash

# Validation script for SSH Multiple User Management Lab V2
# This script validates the setup and configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

check_file() {
    if [ -f "$1" ]; then
        print_success "Found: $1"
        return 0
    else
        print_error "Missing: $1"
        return 1
    fi
}

check_directory() {
    if [ -d "$1" ]; then
        print_success "Found directory: $1"
        return 0
    else
        print_error "Missing directory: $1"
        return 1
    fi
}

# Main validation
print_header "SSH Multiple User Management - Lab V2 Validation"

echo "Starting validation..."
echo ""

# Check basic structure
print_header "Checking Directory Structure"
check_directory "playbooks"
check_directory "roles"
check_directory "inventory"
check_directory "group_vars"
check_directory "host_vars"

echo ""

# Check playbooks
print_header "Checking Playbooks"
check_file "playbooks/gather-facts.yml"
check_file "playbooks/test-connections.yml"
check_file "playbooks/setup-environment.yml"

echo ""

# Check roles
print_header "Checking Roles"
check_directory "roles/ssh-setup"
check_directory "roles/fact-gathering"
check_directory "roles/connection-test"

# Check role structure
for role in ssh-setup fact-gathering connection-test; do
    echo "Checking role: $role"
    check_directory "roles/$role/tasks"
    check_file "roles/$role/tasks/main.yml"
    check_directory "roles/$role/defaults"
    check_file "roles/$role/defaults/main.yml"
    check_directory "roles/$role/meta"
    check_file "roles/$role/meta/main.yml"
done

echo ""

# Check SSH keys
print_header "Checking SSH Keys"
if check_file "roles/ssh-setup/files/controller.pem"; then
    # Check if it's not a placeholder
    if grep -q "Placeholder" "roles/ssh-setup/files/controller.pem"; then
        print_warning "controller.pem appears to be a placeholder"
    else
        print_success "controller.pem appears to be a real key"
    fi
fi

if check_file "roles/ssh-setup/files/labms.pem"; then
    # Check if it's not a placeholder
    if grep -q "Placeholder" "roles/ssh-setup/files/labms.pem"; then
        print_warning "labms.pem appears to be a placeholder"
    else
        print_success "labms.pem appears to be a real key"
    fi
fi

# Check key permissions
echo "Checking SSH key permissions..."
for key in roles/ssh-setup/files/*.pem; do
    if [ -f "$key" ]; then
        perms=$(stat -c "%a" "$key" 2>/dev/null || stat -f "%A" "$key" 2>/dev/null)
        if [ "$perms" = "400" ]; then
            print_success "$(basename $key) has correct permissions (400)"
        else
            print_warning "$(basename $key) has permissions $perms (should be 400)"
        fi
    fi
done

echo ""

# Check inventory files
print_header "Checking Inventory"
check_file "inventory/development.yml"
check_file "inventory/production.yml"

echo ""

# Check configuration files
print_header "Checking Configuration"
check_file "ansible.cfg"
check_file "group_vars/all.yml"

echo ""

# Check documentation
print_header "Checking Documentation"
check_file "README.md"
check_file "QUICK_START.md"
check_file "AWX_SETUP.md"

echo ""

# Ansible syntax check
print_header "Ansible Syntax Validation"
if command -v ansible-playbook >/dev/null 2>&1; then
    echo "Checking playbook syntax..."
    for playbook in playbooks/*.yml; do
        if ansible-playbook --syntax-check "$playbook" >/dev/null 2>&1; then
            print_success "Syntax OK: $(basename $playbook)"
        else
            print_error "Syntax ERROR: $(basename $playbook)"
        fi
    done
else
    print_warning "ansible-playbook not found, skipping syntax check"
fi

echo ""

# Check for ansible-lint
print_header "Linting (Optional)"
if command -v ansible-lint >/dev/null 2>&1; then
    echo "Running ansible-lint..."
    if ansible-lint playbooks/ roles/ >/dev/null 2>&1; then
        print_success "Linting passed"
    else
        print_warning "Linting found issues (run 'ansible-lint playbooks/ roles/' for details)"
    fi
else
    print_warning "ansible-lint not found (install with: pip install ansible-lint)"
fi

echo ""

# Final summary
print_header "Validation Summary"

# Count checks
total_files=0
missing_files=0

# Basic structure files
required_files=(
    "playbooks/gather-facts.yml"
    "playbooks/test-connections.yml"
    "playbooks/setup-environment.yml"
    "roles/ssh-setup/tasks/main.yml"
    "roles/fact-gathering/tasks/main.yml"
    "roles/connection-test/tasks/main.yml"
    "inventory/development.yml"
    "inventory/production.yml"
    "ansible.cfg"
    "group_vars/all.yml"
)

for file in "${required_files[@]}"; do
    total_files=$((total_files + 1))
    if [ ! -f "$file" ]; then
        missing_files=$((missing_files + 1))
    fi
done

present_files=$((total_files - missing_files))

echo "Files checked: $total_files"
echo "Files present: $present_files"
echo "Files missing: $missing_files"

if [ $missing_files -eq 0 ]; then
    print_success "All required files are present!"
    echo ""
    echo "Next steps:"
    echo "1. Update inventory files with your actual hosts"
    echo "2. Replace SSH key placeholders with real keys (if needed)"
    echo "3. Run: make setup"
    echo "4. Run: make test"
else
    print_error "Some required files are missing. Please check the setup."
fi

echo ""
print_header "Validation Complete"
