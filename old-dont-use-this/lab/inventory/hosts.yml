---
# Lab Environment Inventory
# This inventory file contains hosts for the lab environment

all:
  children:
    lab_servers:
      hosts:
        lab-ubuntu-01:
          ansible_host: ************
          # Optional: specify expected OS if known
          expected_os: ubuntu
          expected_user: ubuntu
        
        lab-centos-01:
          ansible_host: ************
          expected_os: centos
          expected_user: centos
        
        lab-rhel-01:
          ansible_host: ************
          expected_os: redhat
          expected_user: cloud-user
        
        lab-unknown-01:
          ansible_host: ************
          # No expected OS/user - will auto-detect
      
      vars:
        # Lab-specific variables
        environment: lab
        ssh_timeout: 10
        
    lab_web_servers:
      hosts:
        lab-web-01:
          ansible_host: ************
        lab-web-02:
          ansible_host: ************
      
      vars:
        server_type: web
        
    lab_db_servers:
      hosts:
        lab-db-01:
          ansible_host: ************
      
      vars:
        server_type: database

  vars:
    # Global variables for all lab hosts
    ansible_ssh_common_args: "-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null"
    ansible_python_interpreter: auto_silent
