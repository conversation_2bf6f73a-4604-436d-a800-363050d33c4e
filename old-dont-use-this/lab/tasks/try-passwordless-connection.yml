---
# Task file for attempting passwordless SSH connection
- name: "Attempt passwordless SSH connection as {{ target_user }}"
  block:
    - name: "Test passwordless connection with {{ target_user }}"
      ansible.builtin.ping:
      vars:
        ansible_user: "{{ target_user }}"
        ansible_ssh_private_key_file: ""
        ansible_ssh_common_args: "-o ConnectTimeout=10 -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o PasswordAuthentication=no -o PubkeyAuthentication=yes"
      register: ping_result

    - name: "Mark successful passwordless connection for {{ inventory_hostname }}"
      set_fact:
        successful_connections: "{{ successful_connections | combine({inventory_hostname: {'user': target_user, 'key': 'passwordless'}}) }}"
        ansible_user: "{{ target_user }}"
        ssh_key_used: "passwordless"
      when: ping_result is succeeded

    - name: "Log successful passwordless connection"
      debug:
        msg: "✓ Successfully connected to {{ inventory_hostname }} as {{ target_user }} using passwordless authentication"
      when: ping_result is succeeded

  rescue:
    - name: "Log failed passwordless connection attempt"
      debug:
        msg: "✗ Failed passwordless connection to {{ inventory_hostname }} as {{ target_user }}"
        verbosity: 2

    - name: "Track failed passwordless connection"
      set_fact:
        failed_connections: "{{ failed_connections | combine({inventory_hostname + '_' + target_user + '_passwordless': 'failed'}) }}"
