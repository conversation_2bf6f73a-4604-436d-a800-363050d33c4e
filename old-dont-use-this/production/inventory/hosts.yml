---
# Production Environment Inventory
# This inventory file contains hosts for the production environment

all:
  children:
    production_servers:
      hosts:
        prod-ubuntu-01:
          ansible_host: *********
          # Optional: specify expected OS if known
          expected_os: ubuntu
          expected_user: ubuntu
        
        prod-centos-01:
          ansible_host: *********
          expected_os: centos
          expected_user: centos
        
        prod-rhel-01:
          ansible_host: *********
          expected_os: redhat
          expected_user: cloud-user
        
        prod-unknown-01:
          ansible_host: *********
          # No expected OS/user - will auto-detect
      
      vars:
        # Production-specific variables
        environment: production
        ssh_timeout: 15
        
    production_web_servers:
      hosts:
        prod-web-01:
          ansible_host: *********
        prod-web-02:
          ansible_host: *********
        prod-web-03:
          ansible_host: *********
      
      vars:
        server_type: web
        
    production_db_servers:
      hosts:
        prod-db-01:
          ansible_host: *********
        prod-db-02:
          ansible_host: *********
      
      vars:
        server_type: database

  vars:
    # Global variables for all production hosts
    ansible_ssh_common_args: "-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null"
    ansible_python_interpreter: auto_silent
