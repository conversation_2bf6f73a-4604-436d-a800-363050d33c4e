---
# Task file for attempting SSH connection with specific user and key
- name: "Attempt SSH connection as {{ target_user }} with {{ ssh_key }}"
  block:
    - name: "Test connection with {{ target_user }} using {{ ssh_key }}"
      ansible.builtin.ping:
      vars:
        ansible_user: "{{ target_user }}"
        ansible_ssh_private_key_file: "/runner/.ssh/{{ ssh_key }}"
        ansible_ssh_common_args: "-o ConnectTimeout=10 -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null"
      register: ping_result

    - name: "Mark successful connection for {{ inventory_hostname }}"
      set_fact:
        successful_connections: "{{ successful_connections | combine({inventory_hostname: {'user': target_user, 'key': ssh_key}}) }}"
        ansible_user: "{{ target_user }}"
        ansible_ssh_private_key_file: "/runner/.ssh/{{ ssh_key }}"
        ssh_key_used: "{{ ssh_key }}"
      when: ping_result is succeeded

    - name: "Log successful connection"
      debug:
        msg: "✓ Successfully connected to {{ inventory_hostname }} as {{ target_user }} using {{ ssh_key }}"
      when: ping_result is succeeded

  rescue:
    - name: "Log failed connection attempt"
      debug:
        msg: "✗ Failed to connect to {{ inventory_hostname }} as {{ target_user }} using {{ ssh_key }}"
        verbosity: 2

    - name: "Track failed connection"
      set_fact:
        failed_connections: "{{ failed_connections | combine({inventory_hostname + '_' + target_user + '_' + ssh_key: 'failed'}) }}"
