- block:
  - name: Force gather facts as redhat user
    remote_user: cloud-user
    ansible.builtin.setup:
    register: result_redhat
    when: result_ubuntu.unreachable is true and result_centos.unreachable is true
    become: true
  rescue:
  - name: Force gather facts as redhat user without sudo
    remote_user: cloud-user
    ansible.builtin.setup:
    register: result_redhat
    when: result_redhat.failed is defined and true
    become: false
