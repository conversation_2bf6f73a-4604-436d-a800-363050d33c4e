- name: Debug SSH Connection Dynamic
  hosts: all
  gather_facts: false
  ignore_unreachable: yes

  vars:
    key_map:
      controller-key: "controller.pem"
      labms: "labms.pem"

  tasks:
    - name: Set image_name (volume or ephemeral)
      set_fact:
        image_name: >-
          {{
            (
              (
                hostvars[inventory_hostname].openstack.volumes[-1].volume_image_metadata.image_name
                if hostvars[inventory_hostname].openstack.volumes is defined and
                  hostvars[inventory_hostname].openstack.volumes | length > 0 and
                  hostvars[inventory_hostname].openstack.volumes[-1].volume_image_metadata is defined and
                  hostvars[inventory_hostname].openstack.volumes[-1].volume_image_metadata.image_name is defined
                else hostvars[inventory_hostname].openstack.image.image_name
              ) | default('', true)
            )
          }}
      when: hostvars[inventory_hostname].openstack is defined
      ignore_errors: true

    - name: Debug image_name
      debug:
        msg: "image_name is set to: {{ image_name }}"

    - name: Set ansible_user dynamically
      set_fact:
        ansible_user: >-
          {{
            image_name | lower
              | regex_replace('.*ubuntu.*', 'ubuntu')
              | regex_replace('.*centos.*', 'centos')
              | regex_replace('.*rocky.*', 'rocky')
              | regex_replace('.*rhel.*', 'cloud-user')
              | default('ubuntu')
          }}
      when: image_name is defined and image_name != ''
      ignore_errors: true

    - name: Set ansible_user to default if not set
      set_fact:
        ansible_user: 'ubuntu'
      when: ansible_user is not defined or ansible_user == 'root'

    - name: Debug ansible_user after setting
      debug:
        msg: "ansible_user after setting is: {{ ansible_user }}"

    - name: Set private_key_file from key_map
      set_fact:
        ansible_ssh_private_key_file: "{{ playbook_dir }}/ssh_keys/{{ key_map[hostvars[inventory_hostname].openstack.key_name] }}"
      when:
        - hostvars[inventory_hostname].openstack.key_name is defined
        - key_map[hostvars[inventory_hostname].openstack.key_name] is defined
      ignore_errors: true

    - name: Display ansible_user and private_key_file
      debug:
        msg: >
          ansible_user is set to: {{ ansible_user }} and
          ansible_ssh_private_key_file is set to: {{ ansible_ssh_private_key_file | default('not defined') }}

    - name: Test SSH connection
      command: hostname
      register: hostname_result
      ignore_errors: true  # Menghindari kegagalan fatal jika host tidak dapat dijangkau

    - name: Display hostname or unreachable message
      debug:
        msg: >
          {% if hostname_result is defined and hostname_result.rc is defined and hostname_result.rc == 0 %}
            The hostname of the host is: {{ hostname_result.stdout }}
          {% else %}
            Instance {{ inventory_hostname }} cannot be accessed.
            {% if hostname_result is defined %}
              Error: {{ hostname_result.stderr | default('No error information available.') }}
            {% else %}
              No error information available.
            {% endif %}
          {% endif %}