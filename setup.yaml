## Play untuk setup multiple key
- hosts: localhost
  become: false
  tasks:
    - file:
        path: /runner/.ssh/
        state: directory

    - copy:
        src: "{{ playbook_dir }}/ssh_keys/controller.pem"
        dest: /runner/.ssh/controller.pem
      delegate_to: localhost
      run_once: true
    - file:
        path: /runner/.ssh/controller.pem
        mode: '0400'

    - copy:
        src: "{{ playbook_dir }}/ssh_keys/labms.pem"
        dest: /runner/.ssh/labms.pem

    - file:
        path: /runner/.ssh/labms.pem
        mode: '0400'

    - name: Tambahkan konfigurasi multiple IdentityFile ke ~/.ssh/config
      blockinfile:
        path: /runner/.ssh/config
        create: yes
        mode: '0600'
        block: |
          Host *
            IdentityFile /runner/.ssh/controller.pem
            IdentityFile /runner/.ssh/labms.pem

## Play untuk ambil anisble_facts variable
- hosts: awx-control-plane,awx-instance-executor,vm-rhel-8.5
  ignore_unreachable: true
  become: false
  gather_facts: false
  tasks:
  - block:
    - name: Force gather facts as ubuntu user
      remote_user: ubuntu
      ansible.builtin.setup:
      register: result_ubuntu
      become: true
    rescue:
    - name: <PERSON><PERSON> facts as ubuntu user without sudo
      remote_user: ubuntu
      ansible.builtin.setup:
      register: result_ubuntu
      become: false
      when: result_ubuntu.failed is defined and true
  - block:
    - name: Force gather facts as centos user
      remote_user: centos
      ansible.builtin.setup:
      register: result_centos
      when: result_ubuntu.unreachable is true
      become: true
    rescue:
    - name: Force gather facts as centos user without sudo
      remote_user: centos
      ansible.builtin.setup:
      register: result_centos
      when: result_centos.failed is defined and true
      become: false
  - block:
    - name: Force gather facts as redhat user
      remote_user: cloud-user
      ansible.builtin.setup:
      register: result_redhat
      when: result_ubuntu.unreachable is true and result_centos.unreachable is true
      become: true
    rescue:
    - name: Force gather facts as redhat user without sudo
      remote_user: cloud-user
      ansible.builtin.setup:
      register: result_redhat
      when: result_redhat.failed is defined and true
      become: false
  - name: test result
    debug:
      msg: "{{inventory_hostname }} OS nya {{ ansible_distribution }}"
